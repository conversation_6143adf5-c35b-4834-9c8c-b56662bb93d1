<template>
  <view class="create-content">
    <!-- 输入区域 -->
    <view class="input-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="edit" size="32" color="#f1c68e" />
          <text>文章内容</text>
        </view>
        <view v-if="selectedFunction !== 'format'" class="example-btn" @click="fillRandomExample">
          <TnIcon name="refresh" size="20" color="#f1c68e" />
          <text>随机示例</text>
        </view>
      </view>
      <view class="textarea-container">
        <textarea 
          v-model="articleContent" 
          :placeholder="placeholderText" 
          class="content-textarea" 
          :maxlength="2500"
          :disabled="loading" 
        />

        <!-- 字数统计和清空按钮 -->
        <view class="textarea-footer">
          <view class="char-count">
            <text class="count-text">{{ articleContent.length }}/2500</text>
          </view>
          <view v-if="articleContent.length > 0" class="clear-btn" @click="clearContent">
            <text class="clear-icon">🗑️</text>
            <text class="clear-text">清空</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能选择 -->
    <view class="options-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="write-feather" size="32" color="#f1c68e" />
          <text>功能选择</text>
        </view>
      </view>

      <!-- 主要功能选择 -->
      <view class="function-selector">
        <view class="function-tabs">
          <view 
            class="function-tab" 
            :class="{ active: selectedFunction === 'generate' }"
            @click="selectedFunction = 'generate'"
          >
            <TnIcon name="add" size="24" :color="selectedFunction === 'generate' ? '#634738' : '#f1c68e'" />
            <text>文章生成</text>
          </view>
          <view 
            class="function-tab" 
            :class="{ active: selectedFunction === 'format' }"
            @click="selectedFunction = 'format'"
          >
            <TnIcon name="edit" size="24" :color="selectedFunction === 'format' ? '#634738' : '#f1c68e'" />
            <text>原文排版</text>
          </view>
          <view 
            class="function-tab" 
            :class="{ active: selectedFunction === 'rewrite' }"
            @click="selectedFunction = 'rewrite'"
          >
            <TnIcon name="refresh" size="24" :color="selectedFunction === 'rewrite' ? '#634738' : '#f1c68e'" />
            <text>改写润色</text>
          </view>
          <view 
            class="function-tab" 
            :class="{ active: selectedFunction === 'continue' }"
            @click="selectedFunction = 'continue'"
          >
            <TnIcon name="right" size="24" :color="selectedFunction === 'continue' ? '#634738' : '#f1c68e'" />
            <text>续写文章</text>
          </view>
        </view>
      </view>

      <!-- 通用选项：排版风格和配图选择 -->
      <view class="common-options">
        <!-- 排版风格选择 -->
        <view class="option-group">
          <view class="option-header">
            <view class="header-left">
              <TnIcon name="inventory" size="24" color="#f1c68e" />
              <text class="option-title">排版风格</text>
            </view>
          </view>
          <view class="style-simple-grid">
            <view
              v-for="style in formatStyles"
              :key="style.value"
              class="style-simple-item"
              :class="{
                active: selectedFormatStyle === style.value
              }"
              @click="handleStyleSelect(style)"
            >
              <text class="style-simple-name">{{ style.name }}</text>
            </view>
          </view>
        </view>

        <!-- 配图选择 -->
        <view class="option-group">
          <view class="option-header">
            <view class="header-left">
              <TnIcon name="image" size="24" color="#f1c68e" />
              <text class="option-title">配图选项</text>
            </view>
          </view>
          <view class="option-toggle">
            <view
              class="toggle-item"
              :class="{
                active: autoGenerateImages
              }"
              @click="handleImageToggle(true)"
            >
              <text>自动配图</text>
            </view>
            <view
              class="toggle-item"
              :class="{ active: !autoGenerateImages }"
              @click="handleImageToggle(false)"
            >
              <text>不配图</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 写作风格选择（仅文章生成功能显示） -->
      <view v-if="selectedFunction === 'generate'" class="generate-options">
        <view class="option-group">
          <view class="option-header">
            <view class="header-left">
              <TnIcon name="edit-form" size="24" color="#f1c68e" />
              <text class="option-title">写作风格</text>
            </view>
          </view>
          <view class="style-grid">
            <view 
              v-for="style in writingStyles" 
              :key="style.value" 
              class="style-item"
              :class="{ active: selectedStyle === style.value }" 
              @click="selectedStyle = style.value"
            >
              <text>{{ style.name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 梦币状态卡片 -->
    <view class="coin-status-section">
      <view class="coin-card">
        <view class="coin-icon">
          <TnIcon name="starry" size="40" color="#f1c68e" />
        </view>
        <view class="coin-content">
          <view class="coin-title">梦币余额</view>
          <view class="coin-desc">
            <view>总余额: {{ totalMxCoin }} 个梦币</view>
            <!-- <view class="coin-detail">
              <text>永久: {{ userMxCoin }} | 临时: {{ userTempCoins }}</text>
            </view> -->
          </view>
        </view>
        <view class="coin-cost">
          <text>{{ wechatMxCoin }}币/次</text>
        </view>
      </view>
    </view>


  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import { formatStyles, writingStyles, generateExamples, rewriteExamples, continueExamples } from '@/utils/wechatAssistantConfig.js'

const vk = uni.vk

// Props
interface Props {
  articleContent: string
  selectedFunction: string
  selectedFormatStyle: string
  selectedStyle: string
  autoGenerateImages: boolean
  loading: boolean
  isVip: boolean
  userMxCoin: number
  userTempCoins: number
  wechatMxCoin: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:articleContent': [value: string]
  'update:selectedFunction': [value: string]
  'update:selectedFormatStyle': [value: string]
  'update:selectedStyle': [value: string]
  'update:autoGenerateImages': [value: boolean]
}>()

// 计算属性
const totalMxCoin = computed(() => {
  return props.userMxCoin + props.userTempCoins
})

const placeholderText = computed(() => {
  const placeholders = {
    generate: '请输入文章主题或关键词...',
    format: '请输入需要排版的文章内容...',
    rewrite: '请输入需要改写润色的文章内容...',
    continue: '请输入需要续写的文章开头...'
  }
  return placeholders[props.selectedFunction as keyof typeof placeholders] || '请输入内容...'
})

// 响应式属性的双向绑定
const articleContent = computed({
  get: () => props.articleContent,
  set: (value) => emit('update:articleContent', value)
})

const selectedFunction = computed({
  get: () => props.selectedFunction,
  set: (value) => emit('update:selectedFunction', value)
})

const selectedFormatStyle = computed({
  get: () => props.selectedFormatStyle,
  set: (value) => emit('update:selectedFormatStyle', value)
})

const selectedStyle = computed({
  get: () => props.selectedStyle,
  set: (value) => emit('update:selectedStyle', value)
})

const autoGenerateImages = computed({
  get: () => props.autoGenerateImages,
  set: (value) => emit('update:autoGenerateImages', value)
})

// 方法
const handleStyleSelect = (style: any) => {
  selectedFormatStyle.value = style.value
}

const handleImageToggle = (enableImages: boolean) => {
  autoGenerateImages.value = enableImages
}

const fillRandomExample = () => {
  let examples = generateExamples

  if (props.selectedFunction === 'rewrite') {
    examples = rewriteExamples
  } else if (props.selectedFunction === 'continue') {
    examples = continueExamples
  }

  const randomIndex = Math.floor(Math.random() * examples.length)
  const randomExample = examples[randomIndex]
  articleContent.value = randomExample.content
}

const clearContent = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空当前输入的内容吗？',
    success: (res) => {
      if (res.confirm) {
        articleContent.value = ''
        vk.toast('内容已清空')
      }
    }
  })
}


</script>

<style lang="scss" scoped>
.create-content {
  .input-section,
  .options-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #fff;

    .title-left {
      display: flex;
      align-items: center;

      text {
        margin-left: 15rpx;
      }
    }

    .example-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      background: rgba(241, 198, 142, 0.1);
      border: 1rpx solid rgba(241, 198, 142, 0.3);
      border-radius: 20rpx;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(241, 198, 142, 0.2);
        border-color: rgba(241, 198, 142, 0.5);
      }

      text {
        font-size: 24rpx;
        color: #f1c68e;
        margin-left: 0;
      }
    }
  }

  .textarea-container {
    position: relative;

    .content-textarea {
      width: 100%;
      min-height: 300rpx;
      padding: 30rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      border-radius: 12rpx;
      font-size: 28rpx;
      line-height: 1.6;
      background: rgba(255, 255, 255, 0.1);
      color: #fff;

      &:focus {
        border-color: #f1c68e;
        background: rgba(255, 255, 255, 0.15);
      }
    }

    .textarea-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16rpx;
      padding: 0 8rpx;

      .char-count {
        .count-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.6);
        }
      }

      .clear-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 12rpx 20rpx;
        background: rgba(255, 77, 79, 0.15);
        border: 1rpx solid rgba(255, 77, 79, 0.3);
        border-radius: 20rpx;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 77, 79, 0.25);
          transform: scale(0.95);
        }

        .clear-icon {
          font-size: 24rpx;
          line-height: 1;
        }

        .clear-text {
          font-size: 24rpx;
          color: #ff4d4f;
          font-weight: 500;
        }
      }
    }
  }

  // 功能选择器
  .function-selector {
    margin-bottom: 30rpx;

    .function-tabs {
      display: flex;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12rpx;
      padding: 6rpx;
      gap: 6rpx;

      .function-tab {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;
        padding: 20rpx 12rpx;
        border-radius: 8rpx;
        transition: all 0.3s ease;
        cursor: pointer;

        &.active {
          background: #f1c68e;
          color: #634738;

          text {
            color: #634738;
          }

          // 激活状态下的图标颜色
          :deep(.tn-icon) {
            color: #634738 !important;
          }
        }

        &:not(.active) {
          color: rgba(255, 255, 255, 0.6);

          text {
            color: rgba(255, 255, 255, 0.6);
          }
        }

        text {
          font-size: 24rpx;
          font-weight: 500;
        }
      }
    }
  }

  // 选项组通用样式
  .option-group {
    margin-top: 20rpx;
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .option-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .header-left {
      display: flex;
      align-items: center;
    }

    .option-title {
      margin-left: 15rpx;
      font-size: 26rpx;
      font-weight: bold;
      color: #fff;
    }

    .vip-tip {
      display: flex;
      align-items: center;
      padding: 4rpx 12rpx;
      background: rgba(241, 198, 142, 0.1);
      border-radius: 12rpx;
      border: 1px solid rgba(241, 198, 142, 0.3);

      text {
        font-size: 20rpx;
        color: #f1c68e;
        margin-left: 4rpx;
      }
    }
  }

  .option-toggle {
    display: flex;
    border-radius: 12rpx;
    overflow: hidden;
    border: 2rpx solid rgba(255, 255, 255, 0.2);

    .toggle-item {
      flex: 1;
      padding: 20rpx;
      text-align: center;
      background: rgba(255, 255, 255, 0.05);
      color: rgba(255, 255, 255, 0.6);
      font-size: 26rpx;
      transition: all 0.3s;
      position: relative;

      &.active {
        background: #f1c68e;
        color: #634738;
      }


    }
  }

  // 风格网格
  .style-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;

    .style-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
      padding: 24rpx 16rpx;
      background: rgba(255, 255, 255, 0.05);
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      transition: all 0.3s ease;
      cursor: pointer;

      &.active {
        background: rgba(241, 198, 142, 0.2);
        border-color: #f1c68e;
        color: #f1c68e;
      }

      &:not(.active) {
        color: rgba(255, 255, 255, 0.6);
      }

      text {
        font-size: 24rpx;
        font-weight: 500;
      }
    }
  }

  // 简约风格网格
  .style-simple-grid {
    display: flex;
    gap: 12rpx;

    .style-simple-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16rpx 20rpx;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8rpx;
      border: 2rpx solid transparent;
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;

      &.active {
        background: rgba(241, 198, 142, 0.1);
        border-color: #f1c68e;
      }



      .style-simple-name {
        font-size: 26rpx;
        font-weight: 500;
        color: #ffffff;
      }

      &.active .style-simple-name {
        color: #f1c68e;
        font-weight: 600;
      }


    }
  }

  // 梦币状态卡片样式
  .coin-status-section {
    margin-bottom: 20rpx;

    .coin-card {
      display: flex;
      align-items: center;
      padding: 24rpx 30rpx;
      border-radius: 12rpx;
      border: 1px solid rgba(241, 198, 142, 0.3);
      background: linear-gradient(135deg, rgba(241, 198, 142, 0.15) 0%, rgba(241, 198, 142, 0.08) 100%);
      transition: all 0.3s ease;
      margin-bottom: 16rpx;

      .coin-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background: rgba(241, 198, 142, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
      }

      .coin-content {
        flex: 1;

        .coin-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8rpx;
        }

        .coin-desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.7);

          .coin-detail {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 4rpx;
          }
        }
      }

      .coin-cost {
        padding: 8rpx 16rpx;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20rpx;
        border: 1px solid rgba(255, 255, 255, 0.2);

        text {
          font-size: 24rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }


}
</style>

<template>
  <view class="account-manage-content">
    <!-- 添加公众号按钮区域 -->
    <view class="add-section">
      <view class="add-btn" @click="addAccount">
        <TnIcon name="plus" size="24" color="#634738" />
        <text>添加公众号</text>
      </view>

      <!-- 使用教程按钮 -->
      <view class="tutorial-btn" @click="showTutorial">
        <TnIcon name="help-circle" size="24" color="#634738" />
        <text>使用教程</text>
      </view>

      <!-- VIP状态提示 -->
      <view v-if="!vipStatus.isGlobalVip" class="vip-tip">
        <TnIcon name="info-circle" size="16" color="#f39c12" />
        <text class="tip-text">非VIP用户只能添加1个公众号</text>
        <text class="upgrade-btn" @click="goToVipPurchase">升级VIP</text>
      </view>
    </view>

    <!-- 公众号列表区域 -->
    <view class="accounts-section">
      <view class="section-title">
        <view class="title-left">
          <TnIcon name="user-group" size="32" color="#f1c68e" />
          <text>我的公众号</text>
        </view>
        <view v-if="!loading && accounts.length > 0" class="refresh-btn" @click="getAccountList">
          <TnIcon name="refresh" size="20" color="#f1c68e" />
          <text>刷新</text>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-section">
        <TnIcon name="loading" size="60" color="#f1c68e" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="accounts.length === 0" class="empty-section">
        <TnIcon name="user-group" size="80" color="rgba(255, 255, 255, 0.3)" />
        <text class="empty-text">暂无公众号</text>
        <text class="empty-desc">点击上方按钮添加您的第一个公众号</text>
      </view>

      <!-- 公众号卡片列表 -->
      <view v-else class="accounts-list">
        <view
          v-for="account in accounts"
          :key="account._id"
          class="account-card"
          @click="viewAccount(account)"
        >
          <!-- 公众号基本信息 -->
          <view class="card-header">
            <view class="info-section">
              <view class="account-name">{{ account.name }}</view>
              <view class="account-desc">{{ account.description || '暂无描述' }}</view>
              <view class="account-time">{{ account._add_time_str }}</view>
            </view>

            <view class="status-section">
              <view
                class="status-badge"
                :class="{ active: account.status === 'active', inactive: account.status === 'inactive' }"
              >
                {{ account.status === 'active' ? '正常' : '异常' }}
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="card-actions">
            <view class="action-btn edit" @click.stop="editAccount(account)">
              <TnIcon name="edit" size="16" color="#f1c68e" />
              <text>编辑</text>
            </view>
            <view class="action-btn delete" @click.stop="deleteAccount(account)">
              <TnIcon name="delete" size="16" color="#ff4757" />
              <text>删除</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用教程弹窗 -->
    <view v-if="showTutorialModal" class="tutorial-modal" @click="closeTutorial">
      <view class="tutorial-content" @click.stop>
        <view class="tutorial-header">
          <text class="tutorial-title">公众号配置教程</text>
          <view class="close-btn" @click="closeTutorial">
            <TnIcon name="close" size="24" color="rgba(255, 255, 255, 0.6)" />
          </view>
        </view>
        <view class="tutorial-body">
          <image 
            :src="tutorialQRCode" 
            class="tutorial-qr" 
            mode="aspectFit"
            @error="onImageError"
          />
          <text class="tutorial-desc">扫描二维码查看详细配置教程</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'

const vk = uni.vk

// 公众号账号接口
interface WechatAccount {
  _id: string
  name: string
  appid: string
  description?: string
  status: 'active' | 'inactive'
  _add_time_str: string
}

// VIP状态接口
interface VipStatus {
  isGlobalVip: boolean
  canAddMore: boolean
}

// 响应式数据
const loading = ref(false)
const accounts = ref<WechatAccount[]>([])
const vipStatus = ref<VipStatus>({
  isGlobalVip: false,
  canAddMore: true
})
const showTutorialModal = ref(false)

// 教程二维码图片地址
const tutorialQRCode = "https://uni-qiniu.88ai.art/static/qrcode_for_gh_2929b025e38a_1280.jpg"

// 检查VIP状态
const checkVipStatus = async () => {
  try {
    // 只检查全局VIP状态
    const globalVip = vk.myfn.checkVip()
    vipStatus.value.isGlobalVip = globalVip
    
    // 根据VIP状态和当前公众号数量判断是否可以添加更多
    if (globalVip) {
      vipStatus.value.canAddMore = true
    } else {
      vipStatus.value.canAddMore = accounts.value.length < 1
    }
  } catch (error) {
    console.error('检查VIP状态失败:', error)
    vipStatus.value.isGlobalVip = false
    vipStatus.value.canAddMore = accounts.value.length < 1
  }
}

// 获取公众号列表
const getAccountList = async () => {
  try {
    loading.value = true
    const res = await vk.callFunction({
      url: 'client/mx/wechat/kh/getAccountList',
      data: {}
    })

    if (res.code === 0) {
      accounts.value = res.data || []
      // 获取列表后检查VIP状态
      await checkVipStatus()
    } else {
      vk.toast(res.msg || '获取公众号列表失败')
    }
  } catch (error) {
    console.error('获取公众号列表失败:', error)
    vk.toast('获取公众号列表失败')
  } finally {
    loading.value = false
  }
}

// 添加公众号
const addAccount = () => {
  // 检查是否可以添加更多公众号
  if (!vipStatus.value.canAddMore) {
    vk.confirm({
      title: '添加限制',
      content: '非VIP用户只能添加1个公众号\n\n是否立即升级VIP享受无限制添加？',
      confirmText: '立即升级',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 跳转到VIP购买页面
          vk.navigateTo({
            url: '/pages/user/member-rights'
          })
        }
      }
    })
    return
  }

  vk.navigateTo({
    url: '/pages/wechat/add-account'
  })
}

// 查看公众号详情
const viewAccount = (account: WechatAccount) => {
  vk.navigateTo({
    url: `/pages/wechat/add-account?id=${account._id}&mode=view`
  })
}

// 编辑公众号
const editAccount = (account: WechatAccount) => {
  vk.navigateTo({
    url: `/pages/wechat/add-account?id=${account._id}&mode=edit`
  })
}

// 删除公众号
const deleteAccount = (account: WechatAccount) => {
  vk.confirm({
    title: '确认删除',
    content: `确定要删除公众号"${account.name}"吗？\n\n删除后无法恢复，请谨慎操作。`,
    confirmText: '确认删除',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        try {
          const deleteRes = await vk.callFunction({
            url: 'client/mx/wechat/kh/deleteAccount',
            data: { _id: account._id }
          })

          if (deleteRes.code === 0) {
            vk.toast('删除成功')
            // 重新获取列表
            getAccountList()
          } else {
            vk.toast(deleteRes.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除公众号失败:', error)
          vk.toast('删除失败')
        }
      }
    }
  })
}

// 显示使用教程
const showTutorial = () => {
  showTutorialModal.value = true
}

// 关闭教程弹窗
const closeTutorial = () => {
  showTutorialModal.value = false
}

// 图片加载失败处理
const onImageError = () => {
  console.error('教程二维码加载失败')
}

// 跳转到VIP购买页面
const goToVipPurchase = () => {
  vk.navigateTo({
    url: '/pages/user/member-rights'
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getAccountList()
})

// 监听公众号变更事件
uni.$on('wechatAccountChanged', () => {
  getAccountList()
})
</script>

<style lang="scss" scoped>
.account-manage-content {
  padding: 0;

  // 添加按钮区域
  .add-section {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .add-btn, .tutorial-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      padding: 24rpx 30rpx;
      background: linear-gradient(135deg, #f1c68e 0%, #e6b366 100%);
      border-radius: 12rpx;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #e6b366 0%, #d9a347 100%);
      }

      text {
        font-size: 28rpx;
        font-weight: 600;
        color: #634738;
      }
    }

    .tutorial-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:active {
        background: rgba(255, 255, 255, 0.15);
      }

      text {
        color: #fff;
      }
    }

    .vip-tip {
      display: flex;
      align-items: center;
      gap: 12rpx;
      padding: 20rpx 24rpx;
      background: rgba(243, 156, 18, 0.1);
      border: 1px solid rgba(243, 156, 18, 0.3);
      border-radius: 8rpx;

      .tip-text {
        flex: 1;
        font-size: 24rpx;
        color: #f39c12;
      }

      .upgrade-btn {
        font-size: 24rpx;
        color: #f1c68e;
        font-weight: 600;
        text-decoration: underline;
      }
    }
  }

  // 公众号列表区域
  .accounts-section {
    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .title-left {
        display: flex;
        align-items: center;
        gap: 12rpx;

        text {
          font-size: 32rpx;
          font-weight: 600;
          color: #fff;
        }
      }

      .refresh-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 12rpx 20rpx;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8rpx;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.1);
        }

        text {
          font-size: 24rpx;
          color: #f1c68e;
        }
      }
    }

    // 加载状态
    .loading-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;
      gap: 20rpx;

      .loading-text {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.6);
      }
    }

    // 空状态
    .empty-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;
      gap: 20rpx;

      .empty-text {
        font-size: 32rpx;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
      }

      .empty-desc {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.5);
        text-align: center;
      }
    }

    // 公众号列表
    .accounts-list {
      display: flex;
      flex-direction: column;
      gap: 20rpx;

      .account-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12rpx;
        padding: 24rpx;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          background: rgba(255, 255, 255, 0.08);
        }

        .card-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .info-section {
            flex: 1;

            .account-name {
              font-size: 28rpx;
              font-weight: 600;
              color: #fff;
              margin-bottom: 8rpx;
            }

            .account-desc {
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.6);
              margin-bottom: 8rpx;
            }

            .account-time {
              font-size: 22rpx;
              color: rgba(255, 255, 255, 0.4);
            }
          }

          .status-section {
            .status-badge {
              padding: 8rpx 16rpx;
              border-radius: 20rpx;
              font-size: 22rpx;
              font-weight: 500;

              &.active {
                background: rgba(39, 174, 96, 0.2);
                color: #27ae60;
                border: 1px solid rgba(39, 174, 96, 0.3);
              }

              &.inactive {
                background: rgba(231, 76, 60, 0.2);
                color: #e74c3c;
                border: 1px solid rgba(231, 76, 60, 0.3);
              }
            }
          }
        }

        .card-actions {
          display: flex;
          gap: 20rpx;

          .action-btn {
            display: flex;
            align-items: center;
            gap: 8rpx;
            padding: 12rpx 20rpx;
            border-radius: 8rpx;
            font-size: 24rpx;
            transition: all 0.3s ease;

            &.edit {
              background: rgba(241, 198, 142, 0.1);
              border: 1px solid rgba(241, 198, 142, 0.3);
              color: #f1c68e;

              &:active {
                background: rgba(241, 198, 142, 0.2);
              }
            }

            &.delete {
              background: rgba(255, 71, 87, 0.1);
              border: 1px solid rgba(255, 71, 87, 0.3);
              color: #ff4757;

              &:active {
                background: rgba(255, 71, 87, 0.2);
              }
            }
          }
        }
      }
    }
  }

  // 教程弹窗
  .tutorial-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .tutorial-content {
      width: 600rpx;
      max-height: 80vh;
      background: #1A1F2B;
      border-radius: 16rpx;
      overflow: hidden;

      .tutorial-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .tutorial-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #fff;
        }

        .close-btn {
          width: 60rpx;
          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          transition: all 0.3s ease;

          &:active {
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }

      .tutorial-body {
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20rpx;

        .tutorial-qr {
          width: 400rpx;
          height: 400rpx;
          border-radius: 12rpx;
          background: #fff;
        }

        .tutorial-desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.6);
          text-align: center;
        }
      }
    }
  }
}
</style>

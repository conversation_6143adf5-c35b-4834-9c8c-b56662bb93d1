<template>
  <view v-if="visible" class="account-selector-overlay" @click="handleOverlayClick">
    <view class="account-selector-popup" @click.stop>
      <!-- 弹窗头部 -->
      <view class="popup-header">
        <view class="header-title">
          <TnIcon name="user-group" size="32" color="#f1c68e" />
          <text>选择公众号</text>
        </view>
        <view class="close-btn" @click="closePopup">
          <TnIcon name="close" size="24" color="rgba(255, 255, 255, 0.6)" />
        </view>
      </view>

      <!-- 公众号列表 -->
      <view class="account-list">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-section">
          <TnIcon name="loading" size="60" color="#f1c68e" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 空状态 -->
        <view v-else-if="accounts.length === 0" class="empty-section">
          <TnIcon name="user-group" size="80" color="rgba(255, 255, 255, 0.3)" />
          <text class="empty-text">暂无可用公众号</text>
          <text class="empty-desc">请先添加公众号账号</text>
          <view class="add-account-btn" @click="addAccount">
            <TnIcon name="plus" size="24" color="#634738" />
            <text>添加公众号</text>
          </view>
        </view>

        <!-- 公众号列表 -->
        <view v-else class="accounts-container">
          <!-- 现有公众号列表 -->
          <view
            v-for="(account, index) in accounts"
            :key="account._id"
            class="account-item"
            :class="{
              selected: selectedAccount?._id === account._id,
              disabled: account.status !== 'active' || (!isVip && index > 0),
              'vip-only': !isVip && index > 0
            }"
            @click="selectAccount(account, index)"
          >
            <view class="account-info">
              <view class="info-content">
                <view class="account-name">{{ account.name }}</view>
                <view class="account-desc">
                  <text v-if="!isVip && index > 0" class="vip-required">需要VIP权限</text>
                  <text v-else>{{ account.description || '暂无描述' }}</text>
                </view>
              </view>
            </view>

            <view class="account-status">
              <view v-if="!isVip && index > 0" class="vip-badge">
                <TnIcon name="crown" size="16" color="#f1c68e" />
                <text>VIP</text>
              </view>
              <view
                v-else
                class="status-badge"
                :class="{ active: account.status === 'active', inactive: account.status !== 'active' }"
              >
                {{ account.status === 'active' ? '正常' : '异常' }}
              </view>

              <view v-if="selectedAccount?._id === account._id" class="selected-icon">
                <TnIcon name="check-circle" size="20" color="#2ed573" />
              </view>
            </view>
          </view>

          <!-- 分割线 -->
          <view class="divider"></view>

          <!-- 添加公众号选项 -->
          <view class="add-account-item" @click="addAccount">
            <view class="add-account-info">
              <view class="add-icon">
                <TnIcon name="plus" size="24" color="#f1c68e" />
              </view>
              <view class="add-content">
                <view class="add-title">添加新公众号</view>
                <view class="add-desc">配置更多公众号账号</view>
              </view>
            </view>
            <view class="add-arrow">
              <TnIcon name="arrow-right" size="20" color="rgba(255, 255, 255, 0.6)" />
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="popup-footer">
        <view class="footer-btn secondary" @click="closePopup">
          <text>取消</text>
        </view>
        <view 
          class="footer-btn primary" 
          :class="{ disabled: !selectedAccount || syncing }"
          @click="confirmSync"
        >
          <TnIcon v-if="syncing" name="loading" size="20" color="#634738" />
          <text>{{ syncing ? '同步中...' : '确认同步' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'

const vk = uni.vk

// 公众号账号接口
interface WechatAccount {
  _id: string
  name: string
  appid: string
  description?: string
  status: 'active' | 'inactive'
}

// Props
interface Props {
  visible: boolean
  articleId: string
  articleTitle: string
  articleContent: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  articleId: '',
  articleTitle: '',
  articleContent: ''
})

// Emits
const emit = defineEmits<{
  close: []
  success: [message: string]
}>()

// 响应式数据
const loading = ref(false)
const syncing = ref(false)
const accounts = ref<WechatAccount[]>([])
const selectedAccount = ref<WechatAccount | null>(null)
const isVip = ref(false)

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    selectedAccount.value = null
    checkVipStatus()
    getAccountList()
  }
})

// 检查VIP状态
const checkVipStatus = async () => {
  try {
    // 只检查全局VIP状态，移除公众号专项VIP检查
    const globalVip = vk.myfn.checkVip()
    isVip.value = globalVip
  } catch (error) {
    console.error('检查VIP状态失败:', error)
    isVip.value = false
  }
}

// 获取公众号列表
const getAccountList = async () => {
  try {
    loading.value = true
    const res = await vk.callFunction({
      url: 'client/mx/wechat/kh/getAccountList',
      data: {}
    })

    if (res.code === 0) {
      accounts.value = res.data || []
    } else {
      vk.toast(res.msg || '获取公众号列表失败')
    }
  } catch (error) {
    console.error('获取公众号列表失败:', error)
    vk.toast('获取公众号列表失败')
  } finally {
    loading.value = false
  }
}

// 选择公众号
const selectAccount = (account: WechatAccount, index: number) => {
  if (account.status !== 'active') {
    vk.toast('该公众号状态异常，无法同步')
    return
  }

  // 检查非VIP用户是否选择了第一个以外的公众号
  if (!isVip.value && index > 0) {
    vk.confirm({
      title: '权限限制',
      content: '非VIP用户只能使用第一个公众号进行同步\n\n是否立即升级VIP享受无限制使用？',
      confirmText: '立即升级',
      cancelText: '取消',
      success: (res: any) => {
        if (res.confirm) {
          // 跳转到VIP购买页面
          closePopup()
          vk.navigateTo({
            url: '/pages/user/member-rights?tab=wechat'
          })
        }
      }
    })
    return
  }

  selectedAccount.value = account
}

// 添加公众号
const addAccount = () => {
  closePopup()
  vk.navigateTo({
    url: '/pages/wechat/add-account'
  })
}

// 关闭弹窗
const closePopup = () => {
  emit('close')
}

// 处理遮罩点击
const handleOverlayClick = () => {
  closePopup()
}

// 检查文章是否包含图片
const hasImages = (content: string) => {
  const imgRegex = /<img[^>]+src="([^"]+)"[^>]*>/g
  const matches = content.match(imgRegex)
  return matches && matches.length > 0
}

// 确认同步
const confirmSync = async () => {
  if (!selectedAccount.value) {
    vk.toast('请选择要同步的公众号')
    return
  }

  if (!props.articleContent) {
    vk.toast('文章内容为空')
    return
  }

  try {
    syncing.value = true

    // 检查是否包含图片，显示相应的加载提示
    const containsImages = hasImages(props.articleContent)
    if (containsImages) {
      vk.showLoading('正在同步...')
    } else {
      vk.showLoading('正在同步...')
    }

    const res = await vk.callFunction({
      url: 'client/mx/wechat/kh/syncToDraft',
      data: {
        account_id: selectedAccount.value._id,
        article_id: props.articleId,
        title: props.articleTitle,
        content: props.articleContent
      }
    })

    if (res.code === 0) {
      // 使用对话框显示成功提示
      vk.confirm({
        title: '同步成功',
        content: '文章已同步到公众号后台，请用手机app公众号助手发布或者电脑网页端发布',
        showCancel: false,
        confirmText: '我知道了',
        success: () => {
          emit('success', '同步成功')
          closePopup()
        }
      })
    } else {
      vk.toast(res.msg || '同步失败')
    }
  } catch (error) {
    console.error('同步失败:', error)
    vk.toast('同步失败')
  } finally {
    syncing.value = false
    vk.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
.account-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
}

.account-selector-popup {
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  background: linear-gradient(135deg, #1A1F2B 0%, #2C3E50 100%);
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);

  .header-title {
    display: flex;
    align-items: center;
    gap: 16rpx;

    text {
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
    }
  }

  .close-btn {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:active {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(0.95);
    }
  }
}

.account-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx;
  max-height: 400rpx;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  gap: 24rpx;

  .loading-text {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  gap: 16rpx;

  .empty-text {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
  }

  .empty-desc {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 24rpx;
  }

  .add-account-btn {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #f1c68e, #e6b87a);
    color: #634738;
    border-radius: 12rpx;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      background: linear-gradient(135deg, #e6b87a, #dba96b);
    }
  }
}

.accounts-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 24rpx 0;
}

// 添加公众号选项样式
.add-account-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(241, 198, 142, 0.08);
  border-radius: 16rpx;
  border: 2rpx dashed rgba(241, 198, 142, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background: rgba(241, 198, 142, 0.12);
    border-color: rgba(241, 198, 142, 0.5);
  }



  .add-account-info {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20rpx;

    .add-icon {
      width: 60rpx;
      height: 60rpx;
      background: rgba(241, 198, 142, 0.15);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .add-content {
      flex: 1;

      .add-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #f1c68e;
        margin-bottom: 8rpx;
      }

      .add-desc {
        font-size: 24rpx;
        color: rgba(241, 198, 142, 0.8);
      }
    }
  }

  .add-arrow {
    opacity: 0.8;
  }
}

// 分割线样式
.divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  margin: 16rpx 0;
}

.account-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &.selected {
    border-color: #f1c68e;
    background: rgba(241, 198, 142, 0.1);
  }

  &.disabled {
    opacity: 0.5;
  }

  &.vip-only {
    opacity: 0.6;
    background: rgba(255, 255, 255, 0.03);
    border: 2rpx dashed rgba(241, 198, 142, 0.3);
  }

  &:not(.disabled):not(.vip-only):active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.1);
  }

  .account-info {
    display: flex;
    align-items: center;
    flex: 1;

    .info-content {
      flex: 1;

      .account-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #fff;
        margin-bottom: 8rpx;
      }

      .account-desc {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.6);

        .vip-required {
          color: #f1c68e;
          font-weight: 500;
        }
      }
    }
  }

  .account-status {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .status-badge {
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-weight: 500;

      &.active {
        background: rgba(46, 213, 115, 0.2);
        color: #2ed573;
      }

      &.inactive {
        background: rgba(255, 107, 107, 0.2);
        color: #ff6b6b;
      }
    }

    .vip-badge {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      background: rgba(241, 198, 142, 0.2);
      color: #f1c68e;
      font-size: 24rpx;
      font-weight: 500;
    }

    .selected-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.popup-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.12);

  .footer-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    height: 88rpx;
    border-radius: 16rpx;
    font-size: 30rpx;
    font-weight: 600;
    transition: all 0.3s ease;

    &.primary {
      background: linear-gradient(135deg, #f1c68e, #e6b87a);
      color: #634738;

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      &:not(.disabled):active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #e6b87a, #dba96b);
      }
    }

    &.secondary {
      background: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:active {
        background: rgba(255, 255, 255, 0.15);
      }
    }
  }
}
</style>

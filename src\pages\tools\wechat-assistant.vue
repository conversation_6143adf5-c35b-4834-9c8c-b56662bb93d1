<template>
  <view class="wechat-assistant-container">
    <!-- 导航栏 -->
    <view class="navbar-section">
      <TnNavbar
        :bottom-shadow="false"
        bg-color="#1A1F2B"
        title-color="#fff"
        @back="vk.navigateBack()"
        @init-finish="onNavbarInit"
      >
        公众号助手
      </TnNavbar>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 标签页切换 -->
      <view class="tab-section">
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ active: currentTab === 'create' }"
            @click="handleTabClick('create')"
          >
            <TnIcon
              name="edit"
              size="20"
              :color="currentTab === 'create' ? '#f1c68e' : 'rgba(255,255,255,0.6)'"
            />
            <text class="tab-text">创建文章</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: currentTab === 'records' }"
            @click="handleTabClick('records')"
          >
            <TnIcon
              name="ticket"
              size="20"
              :color="currentTab === 'records' ? '#f1c68e' : 'rgba(255,255,255,0.6)'"
            />
            <text class="tab-text">文章记录</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: currentTab === 'manage' }"
            @click="handleTabClick('manage')"
          >
            <TnIcon
              name="user-setting"
              size="20"
              :color="currentTab === 'manage' ? '#f1c68e' : 'rgba(255,255,255,0.6)'"
            />
            <text class="tab-text">账号管理</text>
          </view>
        </view>
      </view>

      <!-- 主要内容区域 -->
      <view class="main-content">
        <!-- 创建文章内容 -->
        <scroll-view
          v-if="currentTab === 'create'"
          class="create-tab-container"
          scroll-y
          refresher-enabled
          :refresher-triggered="false"
        >
          <view class="create-tab-content">
            <CreateArticle
              v-model:articleContent="articleContent"
              v-model:selectedFunction="selectedFunction"
              v-model:selectedFormatStyle="selectedFormatStyle"
              v-model:selectedStyle="selectedStyle"
              v-model:autoGenerateImages="autoGenerateImages"
              :loading="loading"
              :isVip="isVip"
              :userMxCoin="userMxCoin"
              :userTempCoins="userTempCoins"
              :wechatMxCoin="wechatMxCoin"
            />
          </view>
        </scroll-view>

        <!-- 文章记录内容 -->
        <view v-if="currentTab === 'records'" class="records-tab-content">
          <ArticleRecords
            v-model:searchKeyword="searchKeyword"
            :articlesLoading="articlesLoading"
            :refreshing="refreshing"
            :loadingMore="loadingMore"
            :hasMore="hasMore"
            :filteredArticles="filteredArticles"
            :validProcessingArticles="validProcessingArticles"
            :navbarHeight="navbarHeight"
            :tabHeight="tabHeight"
            @search="getArticleList()"
            @refresh="onRefresh"
            @refreshRestore="onRefreshRestore"
            @loadMore="onLoadMore"
            @viewArticle="viewArticle"
            @viewProcessing="viewProcessingArticle"
          />
        </view>

        <!-- 账号管理内容 -->
        <scroll-view
          v-if="currentTab === 'manage'"
          class="manage-tab-container"
          scroll-y
          refresher-enabled
          :refresher-triggered="false"
        >
          <view class="manage-tab-content">
            <AccountManage />
          </view>
        </scroll-view>
      </view>

      <!-- 底部按钮区域 - 只在创建文章标签页显示 -->
      <view v-if="currentTab === 'create'" class="bottom-actions">
        <view class="submit-section">
          <TnButton
            :loading="loading"
            :disabled="canAfford && !articleContent.trim() || loading"
            @click="handleGenerateClick"
            bg-color="#f1c68e"
            text-color="#634738"
            size="lg"
            width="500rpx"
            height="80rpx"
            class="generate-button"
          >
            <view class="button-content">
              <text class="button-text">{{ buttonText }}</text>
              <text v-if="canAfford" class="button-cost">{{ wechatMxCoin }}币/次</text>
              <text v-else class="button-cost error">余额不足</text>
            </view>
          </TnButton>
          <!-- 使用提示 -->
          <view v-if="canAfford" class="usage-tip">
            <text>每次使用消费{{ wechatMxCoin }}个梦币，当前总余额{{ totalMxCoin }}个</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { onLoad, onShow, onUnload, onReachBottom } from '@dcloudio/uni-app'
import TnNavbar from '@tuniao/tnui-vue3-uniapp/components/navbar/src/navbar.vue'
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import CreateArticle from '@/components/WechatAssistant/CreateArticle.vue'
import ArticleRecords from '@/components/WechatAssistant/ArticleRecords.vue'
import AccountManage from '@/components/WechatAssistant/AccountManage.vue'
import { useWechatAssistant } from '@/composables/useWechatAssistant.js'

const vk = uni.vk

// 导航栏高度处理
const navbarHeight = ref(88) // 默认高度，单位rpx
const tabHeight = ref(100) // 标签页高度，单位rpx（增加了padding后更高）

// 使用组合式函数
const {
  // 响应式数据
  articleContent,
  loading,
  selectedFunction,
  autoGenerateImages,
  selectedFormatStyle,
  selectedStyle,
  wechatMxCoin,
  userMxCoin,
  userTempCoins,
  isVip,
  isOnShowRunning,
  currentTab,
  articles,
  articlesLoading,
  searchKeyword,
  hasMore,
  loadingMore,
  refreshing,

  // 计算属性
  totalMxCoin,
  canAfford,
  filteredArticles,
  validProcessingArticles,
  buttonText,

  // 方法
  refreshVipStatus,
  getArticleList,
  checkProcessingArticles,
  stopArticlePolling,
  handleGenerateClick,
  viewArticle,
  viewProcessingArticle,
  onRefresh,
  onRefreshRestore,
  onLoadMore
} = useWechatAssistant()

// 处理标签页点击
const handleTabClick = (tab: string) => {
  console.log('点击标签页:', tab)
  currentTab.value = tab

  if (tab === 'records') {
    // 切换到文章记录时，确保加载数据
    if (articles.value.length === 0) {
      getArticleList()
    }
  }
}

// 导航栏初始化完成处理
const onNavbarInit = (info: any) => {
  console.log('导航栏初始化完成:', info)
  // 将px转换为rpx (1px = 2rpx)
  navbarHeight.value = info.height * 2
  console.log('导航栏实际高度:', navbarHeight.value, 'rpx')

  // 延迟测量标签页高度
  setTimeout(() => {
    measureTabHeight()
  }, 100)
}

// 测量标签页实际高度
const measureTabHeight = () => {
  const query = uni.createSelectorQuery()
  query.select('.tab-section').boundingClientRect((data: any) => {
    if (data) {
      // 将px转换为rpx
      tabHeight.value = data.height * 2
      console.log('标签页实际高度:', tabHeight.value, 'rpx')
    }
  }).exec()
}

// 生命周期钩子
onLoad((options) => {
  console.log('微信公众号助手页面加载', options)

  // 处理从文章详情页传递过来的参数
  if (options?.content) {
    try {
      const content = decodeURIComponent(options.content)
      articleContent.value = content
      currentTab.value = 'create' // 切换到创建标签页
      console.log('从URL参数恢复内容:', content)
    } catch (error) {
      console.error('解析URL参数失败:', error)
    }
  }

  if (options?.autoGenerateImages !== undefined) {
    autoGenerateImages.value = options.autoGenerateImages === 'true'
    console.log('URL参数恢复自动配图:', options.autoGenerateImages, '->', autoGenerateImages.value)
  }

  // 处理功能选择参数
  if (options?.selectedFunction) {
    selectedFunction.value = options.selectedFunction
    console.log('URL参数恢复功能类型:', options.selectedFunction)
  }

  if (options?.selectedFormatStyle) {
    selectedFormatStyle.value = options.selectedFormatStyle
    console.log('URL参数恢复排版风格:', options.selectedFormatStyle)
  }

  if (options?.selectedStyle) {
    selectedStyle.value = options.selectedStyle
    console.log('URL参数恢复写作风格:', options.selectedStyle)
  }
})

onShow(async () => {
  if (isOnShowRunning.value) return
  isOnShowRunning.value = true

  try {
    // 刷新VIP状态和用户信息
    await refreshVipStatus()

    // 检查是否有正在处理的文章
    await checkProcessingArticles()

    // 处理从文章详情页返回时的全局参数
    const app = getApp()
    if (app.globalData?.regenerateParams) {
      const params = app.globalData.regenerateParams
      console.log('从全局参数恢复内容:', params)

      articleContent.value = params.content
      autoGenerateImages.value = params.autoGenerateImages

      // 恢复功能选择参数
      if (params.selectedFunction) {
        selectedFunction.value = params.selectedFunction
        console.log('恢复功能类型:', params.selectedFunction)
      }
      if (params.selectedFormatStyle) {
        selectedFormatStyle.value = params.selectedFormatStyle
        console.log('恢复排版风格:', params.selectedFormatStyle)
      }
      if (params.selectedStyle) {
        selectedStyle.value = params.selectedStyle
        console.log('恢复写作风格:', params.selectedStyle)
      }

      console.log('恢复自动配图:', params.autoGenerateImages)

      currentTab.value = 'create' // 切换到创建标签页

      // 清除全局参数，避免重复使用
      delete app.globalData.regenerateParams
    }

    // 如果当前在文章记录标签页，刷新列表
    if (currentTab.value === 'records') {
      getArticleList()
    }
  } catch (error) {
    console.error('页面显示时初始化失败:', error)
  } finally {
    isOnShowRunning.value = false
  }
})

// 监听重新生成事件
uni.$on('regenerateArticle', (params) => {
  console.log('收到重新生成事件:', params)
  articleContent.value = params.content
  autoGenerateImages.value = params.autoGenerateImages

  // 恢复功能选择参数
  if (params.selectedFunction) {
    selectedFunction.value = params.selectedFunction
    console.log('事件恢复功能类型:', params.selectedFunction)
  }
  if (params.selectedFormatStyle) {
    selectedFormatStyle.value = params.selectedFormatStyle
    console.log('事件恢复排版风格:', params.selectedFormatStyle)
  }
  if (params.selectedStyle) {
    selectedStyle.value = params.selectedStyle
    console.log('事件恢复写作风格:', params.selectedStyle)
  }

  console.log('事件恢复自动配图:', params.autoGenerateImages)

  currentTab.value = 'create' // 切换到创建标签页
})

onUnload(() => {
  // 页面卸载时停止轮询
  stopArticlePolling()

  // 清理事件监听
  uni.$off('regenerateArticle')
})

// 监听标签页切换
watch(currentTab, (newTab) => {
  if (newTab === 'records') {
    // 切换到文章记录标签页时，如果还没有数据则加载
    if (articles.value.length === 0) {
      getArticleList()
    }
  }
})

// 上拉加载更多（保持兼容性）
onReachBottom(() => {
  onLoadMore()
})
</script>

<style lang="scss" scoped>
.wechat-assistant-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1A1F2B 0%, #2D3748 100%);

  // 导航栏区域
  .navbar-section {
    flex-shrink: 0;
  }

  // 内容包装器
  .content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 标签页区域
    .tab-section {
      flex-shrink: 0;
      padding: 20rpx 30rpx;
    }

    // 主要内容区域
    .main-content {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    // 底部按钮区域
    .bottom-actions {
      flex-shrink: 0;
      background: linear-gradient(180deg, transparent 0%, rgba(26, 31, 43, 0.95) 30%, rgba(26, 31, 43, 1) 100%);
      backdrop-filter: blur(20rpx);
      padding: 24rpx 30rpx 30rpx;
      display: flex;
      justify-content: center;
    }
  }

  .tab-container {
    display: flex;
    // background: #1A1F2B;
    border-radius: 12rpx;
    padding: 6rpx;

    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      padding: 16rpx 20rpx;
      border-radius: 8rpx;
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.1);

      &.active {
        background: rgba(241, 198, 142, 0.15);
        border: 1px solid rgba(241, 198, 142, 0.3);

        .tab-text {
          color: #f1c68e !important;
          font-weight: 600;
        }
      }

      &:not(.active) {
        .tab-text {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }

      .tab-text {
        font-size: 24rpx;
        transition: all 0.3s ease;
      }
    }
  }

  // 标签页内容样式
  .create-tab-container {
    flex: 1;
    overflow: hidden;

    .create-tab-content {
      padding: 0rpx 30rpx 50rpx;
    }
  }

  .records-tab-content {
    flex: 1;
    overflow: hidden;
    padding: 0rpx 30rpx 50rpx;
  }

  // 账号管理标签页样式
  .manage-tab-container {
    flex: 1;
    overflow: hidden;

    .manage-tab-content {
      padding: 0rpx 30rpx 50rpx;
    }
  }

  .submit-section {
    width: 100%;
    max-width: 650rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .generate-button {
      width: 50% !important;
      min-width: 400rpx !important;
      min-height: 100rpx !important;
      padding: 20rpx 40rpx !important;
      border-radius: 50rpx !important;
    }

    // 更深层的选择器，确保样式生效
    :deep(.generate-button) {
      width: 50% !important;
      min-width: 400rpx !important;
      min-height: 100rpx !important;
    }

    .button-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;

      .button-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #634738;
      }

      .button-cost {
        font-size: 24rpx;
        font-weight: 500;
        color: #634738;
        opacity: 0.8;

        &.error {
          color: #ff4757;
          opacity: 1;
        }
      }
    }

    .usage-tip {
      text-align: center;
      margin-top: 16rpx;
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
</style>
